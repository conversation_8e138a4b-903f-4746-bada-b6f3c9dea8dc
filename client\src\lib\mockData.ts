// Mock data for development - will be replaced by real API calls
export const mockHealthMetrics = {
  heartRate: 72,
  oxygenSaturation: 98,
  steps: 8742,
  sleepHours: 7.5,
  bloodPressureSystolic: 120,
  bloodPressureDiastolic: 80,
  bloodSugar: 95,
  weight: 68.5,
  bmi: 22.5,
  temperature: 98.6
};

export const mockUpcomingAppointments = [
  {
    id: 1,
    doctorName: "Dr. <PERSON>",
    specialty: "Cardiologist",
    hospitalName: "Apollo Hospital, Delhi",
    date: "Tomorrow",
    time: "10:30 AM",
    status: "scheduled"
  },
  {
    id: 2,
    doctorName: "Dr. <PERSON>", 
    specialty: "Ophthalmologist",
    hospitalName: "Max Hospital, Mumbai",
    date: "Jan 25",
    time: "2:00 PM",
    status: "scheduled"
  }
];

export const mockRecentRecords = [
  {
    id: 1,
    title: "Blood Test Report",
    category: "lab_report",
    hospitalName: "Apollo Hospital, Delhi",
    date: "January 15, 2024",
    status: "normal"
  },
  {
    id: 2,
    title: "Chest X-Ray",
    category: "imaging", 
    hospitalName: "Max Hospital, Mumbai",
    date: "January 12, 2024",
    status: "normal"
  },
  {
    id: 3,
    title: "Prescription",
    category: "prescription",
    hospitalName: "Fortis Hospital, Bangalore", 
    date: "January 10, 2024",
    status: "active"
  }
];

export const mockFamilyMembers = [
  {
    id: 1,
    name: "Meera Avadhanula",
    relation: "Mother",
    age: 52,
    bloodGroup: "O+",
    lastCheckup: "Dec 2023",
    initials: "MA"
  },
  {
    id: 2,
    name: "Ravi Avadhanula",
    relation: "Father", 
    age: 55,
    bloodGroup: "B+",
    lastCheckup: "Jan 2024",
    initials: "RA"
  }
];

export const mockUser = {
  id: 1,
  fullName: "Sri Manasvi Avadhanula",
  email: "<EMAIL>",
  patientId: "21011018030",
  bloodGroup: "A+",
  phoneNumber: "+91 **********",
  dateOfBirth: "2003-05-15"
};

// Simulated real-time health metrics updates
export function generateRealtimeMetrics() {
  return {
    heartRate: 68 + Math.floor(Math.random() * 10),
    oxygenSaturation: 96 + Math.floor(Math.random() * 4),
    steps: mockHealthMetrics.steps + Math.floor(Math.random() * 50),
    sleepHours: 7 + Math.random() * 2,
    temperature: 98.2 + Math.random() * 1.2
  };
}

// Mock chart data for health dashboard
export const mockChartData = {
  bloodPressure: [
    { date: "Jan 1", systolic: 118, diastolic: 78 },
    { date: "Jan 8", systolic: 122, diastolic: 82 },
    { date: "Jan 15", systolic: 120, diastolic: 80 },
    { date: "Jan 22", systolic: 119, diastolic: 79 }
  ],
  bloodSugar: [
    { date: "Jan 1", glucose: 92 },
    { date: "Jan 8", glucose: 95 },
    { date: "Jan 15", glucose: 88 },
    { date: "Jan 22", glucose: 94 }
  ],
  weight: [
    { date: "Jan 1", weight: 68.2, bmi: 22.3 },
    { date: "Jan 8", weight: 68.5, bmi: 22.4 },
    { date: "Jan 15", weight: 68.8, bmi: 22.5 },
    { date: "Jan 22", weight: 68.5, bmi: 22.4 }
  ]
};
