import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Shield, Clock, QrCode, Phone, AlertTriangle, Heart, Activity } from 'lucide-react';
import QRCode from 'qrcode';

export default function EmergencyAccess() {
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [countdown, setCountdown] = useState(300); // 5 minutes
  const [otpCode, setOtpCode] = useState('');
  const [isUnlocked, setIsUnlocked] = useState(false);
  const [showFullRecords, setShowFullRecords] = useState(false);

  const emergencyData = {
    name: "<PERSON>",
    age: 45,
    bloodType: "O+",
    allergies: ["Penicillin", "Shellfish"],
    conditions: ["Hypertension", "Type 2 Diabetes"],
    medications: ["Metformin 500mg", "Lisinopril 10mg"],
    emergencyContact: "+****************",
    lastUpdated: new Date().toLocaleDateString()
  };

  useEffect(() => {
    // Generate QR code for emergency access
    const emergencyUrl = `${window.location.origin}/emergency-access?id=emergency-123`;
    QRCode.toDataURL(emergencyUrl, { width: 256 })
      .then(url => setQrCodeUrl(url))
      .catch(console.error);

    // Countdown timer
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          setIsUnlocked(false);
          setShowFullRecords(false);
          return 300; // Reset to 5 minutes
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleOtpSubmit = () => {
    if (otpCode === '123456') { // Mock OTP verification
      setIsUnlocked(true);
      setShowFullRecords(true);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2">
            <Shield className="h-8 w-8 text-red-600" />
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Emergency Access</h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">Quick access to critical medical information</p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* QR Code */}
          <Card className="border-red-200 dark:border-red-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-400">
                <QrCode className="h-5 w-5" />
                Emergency QR Code
              </CardTitle>
              <CardDescription>
                Scan this QR code for instant emergency access
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              {qrCodeUrl && (
                <img src={qrCodeUrl} alt="Emergency QR Code" className="mx-auto rounded-lg" />
              )}
              <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <Clock className="h-4 w-4" />
                Session expires in: {formatTime(countdown)}
              </div>
            </CardContent>
          </Card>

          {/* Emergency Info */}
          <Card className="border-red-200 dark:border-red-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-400">
                <AlertTriangle className="h-5 w-5" />
                Critical Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Blood Type</p>
                  <Badge variant="destructive" className="mt-1">{emergencyData.bloodType}</Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Age</p>
                  <p className="text-lg font-semibold">{emergencyData.age}</p>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Allergies</p>
                <div className="flex flex-wrap gap-2">
                  {emergencyData.allergies.map((allergy, index) => (
                    <Badge key={index} variant="outline" className="border-red-300 text-red-700">
                      {allergy}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Emergency Contact</p>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-green-600" />
                  <span className="font-mono">{emergencyData.emergencyContact}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* OTP Access */}
        {!isUnlocked && (
          <Card className="border-orange-200 dark:border-orange-800">
            <CardHeader>
              <CardTitle>Healthcare Provider Access</CardTitle>
              <CardDescription>
                Enter the OTP code to view full medical records (Demo: 123456)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Enter OTP code"
                  value={otpCode}
                  onChange={(e) => setOtpCode(e.target.value)}
                  className="max-w-xs"
                />
                <Button onClick={handleOtpSubmit} variant="outline">
                  Verify
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Full Medical Records */}
        {showFullRecords && (
          <Card className="border-green-200 dark:border-green-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-700 dark:text-green-400">
                <Heart className="h-5 w-5" />
                Complete Medical Profile
              </CardTitle>
              <CardDescription>
                Full access granted • Last updated: {emergencyData.lastUpdated}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Medical Conditions
                  </h3>
                  <div className="space-y-2">
                    {emergencyData.conditions.map((condition, index) => (
                      <Badge key={index} variant="secondary" className="mr-2">
                        {condition}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold mb-3">Current Medications</h3>
                  <div className="space-y-2">
                    {emergencyData.medications.map((medication, index) => (
                      <div key={index} className="bg-gray-50 dark:bg-gray-800 p-2 rounded">
                        <p className="text-sm font-medium">{medication}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Recent Vitals */}
              <div>
                <h3 className="font-semibold mb-3">Recent Vitals</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg text-center">
                    <p className="text-2xl font-bold text-blue-700 dark:text-blue-400">120/80</p>
                    <p className="text-xs text-blue-600 dark:text-blue-500">Blood Pressure</p>
                  </div>
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg text-center">
                    <p className="text-2xl font-bold text-red-700 dark:text-red-400">72</p>
                    <p className="text-xs text-red-600 dark:text-red-500">Heart Rate</p>
                  </div>
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg text-center">
                    <p className="text-2xl font-bold text-green-700 dark:text-green-400">98.6°F</p>
                    <p className="text-xs text-green-600 dark:text-green-500">Temperature</p>
                  </div>
                  <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg text-center">
                    <p className="text-2xl font-bold text-purple-700 dark:text-purple-400">99%</p>
                    <p className="text-xs text-purple-600 dark:text-purple-500">SpO2</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}