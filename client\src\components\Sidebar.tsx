import { Link, useLocation } from "wouter";
import { useTheme } from "@/hooks/useTheme";
import { 
  Stethoscope, 
  LayoutDashboard, 
  FolderPlus, 
  CalendarCheck, 
  Users, 
  BrainCircuit, 
  ShieldAlert,
  X,
  Menu
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarContent, AvatarFallback } from "@/components/ui/avatar";
import { mockUser } from "@/lib/mockData";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  onToggle: () => void;
}

const navigation = [
  { name: "Dashboard", href: "/", icon: LayoutDashboard, color: "text-blue-600" },
  { name: "Medical Records", href: "/records", icon: FolderPlus, color: "text-yellow-500", accent: "records-accent" },
  { name: "Appointments", href: "/appointments", icon: CalendarCheck, color: "text-pink-500", accent: "appointments-accent" },
  { name: "Family Vault", href: "/family", icon: Users, color: "text-green-500", accent: "family-vault-accent" },
  { name: "AI Assistant", href: "/assistant", icon: BrainCircuit, color: "text-orange-500", accent: "ai-accent" },
  { name: "Emergency Access", href: "/emergency", icon: ShieldAlert, color: "text-red-500", accent: "emergency-accent" },
];

export default function Sidebar({ isOpen, onClose, onToggle }: SidebarProps) {
  const [location] = useLocation();
  const { theme, toggleTheme } = useTheme();

  return (
    <>
      {/* Mobile header */}
      <header className="lg:hidden bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 p-4 relative z-20">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="p-2"
          >
            <Menu className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">MediKey Dashboard</h1>
          <Avatar className="w-8 h-8">
            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs font-bold">
              SA
            </AvatarFallback>
          </Avatar>
        </div>
      </header>

      {/* Sidebar overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside 
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out ${
          isOpen ? "translate-x-0" : "-translate-x-full"
        } lg:translate-x-0`}
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-orange-500 rounded-lg flex items-center justify-center">
              <Stethoscope className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold">MediKey</h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">Digital Health Records</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="lg:hidden p-1"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        
        <nav className="p-4 space-y-2">
          {navigation.map((item) => {
            const isActive = location === item.href;
            const Icon = item.icon;
            
            return (
              <Link key={item.name} href={item.href}>
                <a
                  className={`flex items-center space-x-3 p-3 rounded-lg transition-colors group ${
                    isActive
                      ? "bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium"
                      : "hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
                  onClick={onClose}
                >
                  <Icon className={`w-5 h-5 ${isActive ? "text-blue-600" : item.color}`} />
                  <span>{item.name}</span>
                  {!isActive && (
                    <div className={`w-2 h-2 rounded-full ml-auto ${
                      item.accent === "records-accent" ? "bg-yellow-400" :
                      item.accent === "appointments-accent" ? "bg-pink-400" :
                      item.accent === "family-vault-accent" ? "bg-green-400" :
                      item.accent === "ai-accent" ? "bg-orange-400" :
                      item.accent === "emergency-accent" ? "bg-red-400" : ""
                    }`} />
                  )}
                </a>
              </Link>
            );
          })}
        </nav>

        <div className="absolute bottom-4 left-4 right-4 space-y-4">
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <span className="text-sm font-medium">Theme</span>
            <Switch
              checked={theme === "dark"}
              onCheckedChange={toggleTheme}
              className="data-[state=checked]:bg-blue-600"
            />
          </div>
          <div className="flex items-center space-x-3 p-3 border-t border-gray-200 dark:border-gray-700">
            <Avatar className="w-8 h-8">
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs font-bold">
                SA
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <p className="text-sm font-medium">{mockUser.fullName.split(' ')[0]} {mockUser.fullName.split(' ')[1]}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Patient ID: {mockUser.patientId}</p>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}
