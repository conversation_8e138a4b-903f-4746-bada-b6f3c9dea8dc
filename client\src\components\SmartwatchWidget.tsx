import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Activity, Thermometer, Moon, Footprints, Zap } from 'lucide-react';

interface SmartWatchMetrics {
  heartRate: number;
  spO2: number;
  temperature: number;
  sleepHours: number;
  steps: number;
  batteryLevel: number;
  lastSync: Date;
}

export default function SmartwatchWidget() {
  const [metrics, setMetrics] = useState<SmartWatchMetrics>({
    heartRate: 72,
    spO2: 98,
    temperature: 98.6,
    sleepHours: 7.5,
    steps: 8432,
    batteryLevel: 85,
    lastSync: new Date()
  });

  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        heartRate: Math.floor(Math.random() * (85 - 65) + 65),
        spO2: Math.floor(Math.random() * (100 - 95) + 95),
        temperature: parseFloat((Math.random() * (99.5 - 97.5) + 97.5).toFixed(1)),
        steps: prev.steps + Math.floor(Math.random() * 10),
        batteryLevel: Math.max(0, prev.batteryLevel - (Math.random() > 0.9 ? 1 : 0)),
        lastSync: new Date()
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getHeartRateStatus = (hr: number) => {
    if (hr < 60) return { status: 'Low', color: 'text-blue-600' };
    if (hr > 100) return { status: 'High', color: 'text-red-600' };
    return { status: 'Normal', color: 'text-green-600' };
  };

  const getSpO2Status = (spo2: number) => {
    if (spo2 < 95) return { status: 'Low', color: 'text-red-600' };
    return { status: 'Normal', color: 'text-green-600' };
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-blue-600" />
            Smartwatch Integration
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className={`h-2 w-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-xs text-gray-500">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Last sync: {metrics.lastSync.toLocaleTimeString()}
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {/* Heart Rate */}
          <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Heart className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium">Heart Rate</span>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-red-700 dark:text-red-400">
                {metrics.heartRate}
                <span className="text-sm font-normal ml-1">bpm</span>
              </p>
              <Badge 
                variant="outline" 
                className={`text-xs ${getHeartRateStatus(metrics.heartRate).color}`}
              >
                {getHeartRateStatus(metrics.heartRate).status}
              </Badge>
            </div>
          </div>

          {/* SpO2 */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Activity className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">SpO2</span>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-blue-700 dark:text-blue-400">
                {metrics.spO2}
                <span className="text-sm font-normal ml-1">%</span>
              </p>
              <Badge 
                variant="outline" 
                className={`text-xs ${getSpO2Status(metrics.spO2).color}`}
              >
                {getSpO2Status(metrics.spO2).status}
              </Badge>
            </div>
          </div>

          {/* Temperature */}
          <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Thermometer className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium">Temperature</span>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-orange-700 dark:text-orange-400">
                {metrics.temperature}
                <span className="text-sm font-normal ml-1">°F</span>
              </p>
              <Badge variant="outline" className="text-xs text-green-600">
                Normal
              </Badge>
            </div>
          </div>

          {/* Sleep */}
          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Moon className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium">Sleep</span>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-purple-700 dark:text-purple-400">
                {metrics.sleepHours}
                <span className="text-sm font-normal ml-1">hrs</span>
              </p>
              <Badge variant="outline" className="text-xs text-green-600">
                Good
              </Badge>
            </div>
          </div>

          {/* Steps */}
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Footprints className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Steps</span>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-green-700 dark:text-green-400">
                {metrics.steps.toLocaleString()}
              </p>
              <Badge variant="outline" className="text-xs text-green-600">
                84% of goal
              </Badge>
            </div>
          </div>

          {/* Battery */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium">Battery</span>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-700 dark:text-gray-400">
                {metrics.batteryLevel}
                <span className="text-sm font-normal ml-1">%</span>
              </p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${metrics.batteryLevel}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Sync Status */}
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-800 dark:text-blue-400">
              Real-time monitoring active
            </span>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-blue-600 dark:text-blue-400">Live</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}