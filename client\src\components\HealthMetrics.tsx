import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Heart, Droplets, Footprints, Bed } from "lucide-react";
import { mockHealthMetrics, generateRealtimeMetrics } from "@/lib/mockData";

export default function HealthMetrics() {
  const [metrics, setMetrics] = useState(mockHealthMetrics);

  useEffect(() => {
    // Simulate real-time updates every 30 seconds
    const interval = setInterval(() => {
      const newMetrics = generateRealtimeMetrics();
      setMetrics(prev => ({ ...prev, ...newMetrics }));
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const metricItems = [
    {
      icon: Heart,
      label: "Heart Rate",
      value: `${metrics.heartRate} BPM`,
      bgColor: "bg-red-50 dark:bg-red-900/20",
      borderColor: "border-red-200 dark:border-red-800",
      iconColor: "text-red-500"
    },
    {
      icon: Droplets,
      label: "Oxygen",
      value: `${metrics.oxygenSaturation}%`,
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
      borderColor: "border-blue-200 dark:border-blue-800",
      iconColor: "text-blue-500"
    },
    {
      icon: Footprints,
      label: "Steps",
      value: metrics.steps.toLocaleString(),
      bgColor: "bg-green-50 dark:bg-green-900/20",
      borderColor: "border-green-200 dark:border-green-800",
      iconColor: "text-green-500"
    },
    {
      icon: Bed,
      label: "Sleep",
      value: `${metrics.sleepHours} hrs`,
      bgColor: "bg-purple-50 dark:bg-purple-900/20",
      borderColor: "border-purple-200 dark:border-purple-800",
      iconColor: "text-purple-500"
    }
  ];

  return (
    <Card className="mb-8">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold flex items-center space-x-2">
            <div className="w-5 h-5 bg-blue-600 rounded flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full" />
            </div>
            <span>Real-time Health Metrics</span>
          </h2>
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span>Live</span>
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {metricItems.map((item, index) => {
            const Icon = item.icon;
            return (
              <div
                key={index}
                className={`p-4 ${item.bgColor} rounded-lg border ${item.borderColor} health-metric-card`}
              >
                <div className="flex items-center space-x-3">
                  <Icon className={`w-6 h-6 ${item.iconColor}`} />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{item.label}</p>
                    <p className={`text-xl font-bold ${item.iconColor}`}>{item.value}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
