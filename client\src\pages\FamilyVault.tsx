import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Users, Plus, FileText, Calendar, Heart, Baby, UserPlus } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

export default function FamilyVault() {
  const [newMemberDialog, setNewMemberDialog] = useState(false);
  const [newMember, setNewMember] = useState({
    name: '',
    relationship: '',
    dateOfBirth: '',
    bloodType: '',
    emergencyContact: ''
  });

  const queryClient = useQueryClient();

  const { data: familyMembers = [], isLoading } = useQuery({
    queryKey: ['/api/family-members'],
    queryFn: () => Promise.resolve([
      {
        id: 1,
        name: "Sarah Wilson",
        relationship: "spouse",
        dateOfBirth: "1982-05-15",
        bloodType: "A+",
        emergencyContact: "+1 (555) 987-6543",
        createdAt: "2024-01-15",
        recordCount: 12
      },
      {
        id: 2,
        name: "Emma Wilson",
        relationship: "child",
        dateOfBirth: "2015-09-22",
        bloodType: "O+",
        emergencyContact: "+1 (555) 987-6543",
        createdAt: "2024-01-15",
        recordCount: 8
      }
    ])
  });

  const addMemberMutation = useMutation({
    mutationFn: async (memberData: any) => {
      return new Promise((resolve) => {
        setTimeout(() => resolve({ id: Date.now(), ...memberData }), 1000);
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/family-members'] });
      setNewMemberDialog(false);
      setNewMember({
        name: '',
        relationship: '',
        dateOfBirth: '',
        bloodType: '',
        emergencyContact: ''
      });
    }
  });

  const handleAddMember = () => {
    addMemberMutation.mutate(newMember);
  };

  const getRelationshipIcon = (relationship: string) => {
    switch (relationship.toLowerCase()) {
      case 'child':
      case 'daughter':
      case 'son':
        return <Baby className="h-5 w-5 text-green-600" />;
      case 'spouse':
      case 'husband':
      case 'wife':
        return <Heart className="h-5 w-5 text-red-600" />;
      default:
        return <Users className="h-5 w-5 text-blue-600" />;
    }
  };

  const getAgeFromBirth = (dateOfBirth: string) => {
    const today = new Date();
    const birth = new Date(dateOfBirth);
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1;
    }
    return age;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Users className="h-8 w-8 text-green-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Family Vault</h1>
              <p className="text-gray-600 dark:text-gray-300">Manage family medical records and information</p>
            </div>
          </div>
          
          <Dialog open={newMemberDialog} onOpenChange={setNewMemberDialog}>
            <DialogTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Family Member
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Family Member</DialogTitle>
                <DialogDescription>
                  Add a family member to manage their medical information
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={newMember.name}
                    onChange={(e) => setNewMember(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter full name"
                  />
                </div>
                
                <div>
                  <Label htmlFor="relationship">Relationship</Label>
                  <Select value={newMember.relationship} onValueChange={(value) => setNewMember(prev => ({ ...prev, relationship: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select relationship" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="spouse">Spouse</SelectItem>
                      <SelectItem value="child">Child</SelectItem>
                      <SelectItem value="parent">Parent</SelectItem>
                      <SelectItem value="sibling">Sibling</SelectItem>
                      <SelectItem value="grandparent">Grandparent</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="dateOfBirth">Date of Birth</Label>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={newMember.dateOfBirth}
                    onChange={(e) => setNewMember(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                  />
                </div>
                
                <div>
                  <Label htmlFor="bloodType">Blood Type</Label>
                  <Select value={newMember.bloodType} onValueChange={(value) => setNewMember(prev => ({ ...prev, bloodType: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select blood type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="A+">A+</SelectItem>
                      <SelectItem value="A-">A-</SelectItem>
                      <SelectItem value="B+">B+</SelectItem>
                      <SelectItem value="B-">B-</SelectItem>
                      <SelectItem value="AB+">AB+</SelectItem>
                      <SelectItem value="AB-">AB-</SelectItem>
                      <SelectItem value="O+">O+</SelectItem>
                      <SelectItem value="O-">O-</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="emergencyContact">Emergency Contact</Label>
                  <Input
                    id="emergencyContact"
                    value={newMember.emergencyContact}
                    onChange={(e) => setNewMember(prev => ({ ...prev, emergencyContact: e.target.value }))}
                    placeholder="Phone number"
                  />
                </div>
                
                <Button 
                  onClick={handleAddMember} 
                  disabled={addMemberMutation.isPending || !newMember.name || !newMember.relationship}
                  className="w-full"
                >
                  {addMemberMutation.isPending ? 'Adding...' : 'Add Family Member'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Family Members Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {isLoading ? (
            <div className="col-span-full text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading family members...</p>
            </div>
          ) : familyMembers.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <UserPlus className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No family members added</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">Start by adding your family members to manage their medical records</p>
              <Button onClick={() => setNewMemberDialog(true)} className="bg-green-600 hover:bg-green-700">
                <Plus className="h-4 w-4 mr-2" />
                Add First Family Member
              </Button>
            </div>
          ) : (
            familyMembers.map((member: any) => (
              <Card key={member.id} className="hover:shadow-lg transition-shadow border-green-200 dark:border-green-800">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      {getRelationshipIcon(member.relationship)}
                      <div>
                        <CardTitle className="text-lg">{member.name}</CardTitle>
                        <CardDescription className="capitalize">
                          {member.relationship} • {member.dateOfBirth ? `${getAgeFromBirth(member.dateOfBirth)} years old` : 'Age not set'}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">
                      {member.bloodType || 'Unknown'}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="font-medium text-gray-600 dark:text-gray-400">Date of Birth</p>
                      <p className="text-gray-900 dark:text-white">
                        {member.dateOfBirth ? new Date(member.dateOfBirth).toLocaleDateString() : 'Not set'}
                      </p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-600 dark:text-gray-400">Emergency Contact</p>
                      <p className="text-gray-900 dark:text-white font-mono text-xs">
                        {member.emergencyContact || 'Not set'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="font-medium text-gray-600 dark:text-gray-400 text-sm">Quick Actions</p>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <FileText className="h-3 w-3 mr-1" />
                        Records
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Calendar className="h-3 w-3 mr-1" />
                        Appointments
                      </Button>
                    </div>
                  </div>
                  
                  {/* Recent Activity */}
                  <div className="border-t pt-3">
                    <p className="font-medium text-gray-600 dark:text-gray-400 text-sm mb-2">Recent Activity</p>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span>Profile created {new Date(member.createdAt || Date.now()).toLocaleDateString()}</span>
                      </div>
                      {member.lastCheckup && (
                        <div className="flex items-center gap-2 text-xs text-gray-600">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          <span>Last checkup: {new Date(member.lastCheckup).toLocaleDateString()}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Family Health Summary */}
        {familyMembers.length > 0 && (
          <Card className="border-green-200 dark:border-green-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5 text-green-600" />
                Family Health Overview
              </CardTitle>
              <CardDescription>
                Quick overview of your family's health status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <h3 className="font-semibold text-green-800 dark:text-green-400 mb-2">Total Members</h3>
                  <p className="text-2xl font-bold text-green-700 dark:text-green-300">{familyMembers.length}</p>
                </div>
                
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-800 dark:text-blue-400 mb-2">Upcoming Checkups</h3>
                  <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                    {familyMembers.filter((m: any) => m.nextCheckup).length}
                  </p>
                </div>
                
                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                  <h3 className="font-semibold text-purple-800 dark:text-purple-400 mb-2">Health Records</h3>
                  <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                    {familyMembers.reduce((total: number, member: any) => total + (member.recordCount || 0), 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}