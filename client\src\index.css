@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(210, 40%, 98%);
  --foreground: hsl(222.2, 84%, 4.9%);
  --muted: hsl(210, 40%, 96%);
  --muted-foreground: hsl(215.4, 16.3%, 46.9%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(222.2, 84%, 4.9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(222.2, 84%, 4.9%);
  --border: hsl(214.3, 31.8%, 91.4%);
  --input: hsl(214.3, 31.8%, 91.4%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(210, 40%, 96%);
  --secondary-foreground: hsl(222.2, 84%, 4.9%);
  --accent: hsl(210, 40%, 96%);
  --accent-foreground: hsl(222.2, 84%, 4.9%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(222.2, 84%, 4.9%);
  --radius: 0.5rem;
  
  /* MediKey theme colors */
  --navy-deep: hsl(220, 61%, 15%);
  --sky-light: hsl(210, 100%, 95%);
  --family-vault: hsl(134, 43%, 50%);
  --records-yellow: hsl(51, 100%, 50%);
  --appointments-pink: hsl(330, 100%, 71%);
  --ai-orange: hsl(39, 100%, 50%);
  --emergency-red: hsl(9, 100%, 64%);
}

.dark {
  --background: hsl(220, 61%, 15%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(217.2, 32.6%, 17.5%);
  --muted-foreground: hsl(215, 20.2%, 65.1%);
  --popover: hsl(222.2, 84%, 4.9%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(222.2, 84%, 4.9%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(217.2, 32.6%, 17.5%);
  --input: hsl(217.2, 32.6%, 17.5%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(222.2, 84%, 4.9%);
  --secondary: hsl(217.2, 32.6%, 17.5%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(217.2, 32.6%, 17.5%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(212.7, 26.8%, 83.9%);
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans antialiased;
    background-color: var(--sky-light);
  }
  
  .dark body {
    background-color: var(--navy-deep);
  }
}

/* Custom module colors */
.family-vault-accent {
  color: var(--family-vault);
}

.records-accent {
  color: var(--records-yellow);
}

.appointments-accent {
  color: var(--appointments-pink);
}

.ai-accent {
  color: var(--ai-orange);
}

.emergency-accent {
  color: var(--emergency-red);
}

/* Molecular animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.molecule-float {
  animation: float 6s ease-in-out infinite;
}

.molecule-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

/* Health metrics cards */
.health-metric-card {
  transition: all 0.3s ease;
}

.health-metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .health-metric-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Sidebar animations */
.sidebar-enter {
  transform: translateX(-100%);
}

.sidebar-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

.sidebar-exit {
  transform: translateX(0);
}

.sidebar-exit-active {
  transform: translateX(-100%);
  transition: transform 300ms ease-in-out;
}

/* Tab animations */
.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Chat message animations */
.chat-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { 
    opacity: 0; 
    transform: translateX(-20px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(0); 
  }
}

/* Loading pulse */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}
