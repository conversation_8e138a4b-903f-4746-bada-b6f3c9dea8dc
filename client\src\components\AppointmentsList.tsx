import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, UserCheck, Eye } from "lucide-react";
import { mockUpcomingAppointments } from "@/lib/mockData";

export default function AppointmentsList() {
  return (
    <Card className="mb-8">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-pink-500" />
            <span>Upcoming Appointments</span>
          </h2>
          <Button variant="outline" size="sm" className="text-blue-600 hover:text-blue-700">
            View All
          </Button>
        </div>
        
        <div className="space-y-4">
          {mockUpcomingAppointments.map((appointment) => (
            <div
              key={appointment.id}
              className="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-pink-500/50 transition-colors"
            >
              <div className="w-12 h-12 bg-pink-500/20 rounded-lg flex items-center justify-center">
                <UserCheck className="w-6 h-6 text-pink-500" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium">{appointment.doctorName}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{appointment.specialty}</p>
                <p className="text-xs text-gray-500 dark:text-gray-500">{appointment.hospitalName}</p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">{appointment.date}</p>
                <p className="text-xs text-gray-500">{appointment.time}</p>
              </div>
              <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100">
                <Eye className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
