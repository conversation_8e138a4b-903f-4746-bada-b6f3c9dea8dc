🧠 Prompt Start — "Create MediKey"
I want you to build a fully functional, responsive medical record system called MediKey – Universal Digital Medical Record System using React + TypeScript + Tailwind CSS in Replit. Follow the exact layout, color schemes, and features based on this description:

🧱 TECH STACK
Frontend: React + TypeScript + Tailwind CSS

Backend (optional): Node.js + Firebase or mock API

Extras:

Framer Motion or Tailwind transitions

Recharts for health analytics

Lucide React for icons

Use .env for Firebase & XAI keys

🌗 THEME SUPPORT
Dark Theme Background: Deep Navy Blue (#0B1E3F)

Light Theme Background: Sky Blue (#E6F0FF)

Tailwind dark mode enabled

Toggle switch to change themes

🎨 COLOR PALETTE (Module-Based Accent Colors)
Module	Color
Family Vault	Green (#3CB371)
Records	Yellow (#FFD700)
Appointments	Pink (#FF69B4)
AI Assistant	Orange (#FFA500)
Emergency Access	Red (#FF6347)

Each module/card/tab should have this color in accent borders, badges, or icons.

🧩 UI STRUCTURE (app/page.tsx)
Main Layout
min-h-screen, flex container

Sidebar (fixed, w-64, visible on desktop, drawer on mobile)

Main section:

AnimatedBackground (molecule animation using SVG or canvas)

Container (p-6, relative, z-10)

Sections:

Welcome message with full name

Smartwatch integration widget (real-time metrics)

Quick Actions

Appointments Overview

Tabbed health dashboard: Health | Records | Family

🧬 SMARTWATCH INTEGRATION
Card with metrics like: heart rate, oxygen, steps, temp, sleep

Grid layout (grid-cols-2 md:grid-cols-4)

Each metric: Icon + Label + Value

Simulated values from mockData.ts

🤖 AI HEALTH ASSISTANT (/assistant page)
Powered by Grok via XAI_API_KEY

Layout:

Header with title, subtitle

Clear chat button

Chat area (user vs assistant messages)

Input box with voice mic + send button

Assistant responses should:

Use user’s health data

Be helpful and polite

Styled in orange bubble (light and dark variants)

Environment:

env
Copy
Edit
XAI_API_KEY=your_api_key
📱 AUTH PAGES (/auth/signin, /auth/signup)
Sign-in and Sign-up forms:

FullName, Email, Password

Card-style layout

Tailwind forms

Firebase Auth (VITE vars below)

Environment:

env
Copy
Edit
VITE_FIREBASE_API_KEY=your_key
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_APP_ID=your_app_id
💡 FUNCTIONAL FEATURES
✅ Core
Upload PDFs/images with categorization (mock or real upload)

Record tagging and searching

Emergency QR Access simulation (button + mock QR)

Doctor login → view patient history and notes

Summary via AI for each record (NLP style, mocked)

✅ Smart Dashboard Tabs
Tabs: Health Dashboard | Recent Records | Family Vault

Charts: BP, Glucose, BMI using Recharts

Animated cards for each metric

✅ Family Vault
Grid of dependents

Switch profile

Color-coded green accent

✅ Appointments
Card-style upcoming visits

Calendar icon

Set new appointments

🔐 Security Features (Optional Simulated)
Simulated access logs

OTP-based access popup (mocked)

Doctor/patient roles (use simple role switch)

🧠 Advanced AI Assistant
/assistant uses Grok model

Voice input (simulated 5-sec timer)

History of previous messages stored in memory

Categories: Medication queries, Summaries, Diet Advice

🛡️ Privacy & Offline
Simulate sync logic on offline/online switch

Mobile caching mock

Encrypted badge icons

🌐 Multilingual UI (Optional Extension)
English + Hindi toggles

Use Tailwind and react-i18n for translation support

🔄 Responsive Design
Mobile-first design

Drawer for sidebar on small screens

Cards collapse into single-column

🎯 Deployment-Ready
Firebase Hosting / Vercel setup

Use .env for API keys

Fully styled, no broken UI

Animated molecule background always behind main content

💬 Bonus Components
Toasts for reminders & uploads

Notifications dropdown

Badge for user’s health risk index (e.g., “Medium Risk”)