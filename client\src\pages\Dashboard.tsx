import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useTheme } from "@/hooks/useTheme";
import AnimatedBackground from "@/components/AnimatedBackground";
import Sidebar from "@/components/Sidebar";
import HealthMetrics from "@/components/HealthMetrics";
import QuickActions from "@/components/QuickActions";
import AppointmentsList from "@/components/AppointmentsList";
import TabbedHealthDashboard from "@/components/TabbedHealthDashboard";
import SmartwatchWidget from "@/components/SmartwatchWidget";
import AIAssistant from "@/components/AIAssistant";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Bell, ShieldCheck, LogOut, Moon, Sun } from "lucide-react";
import { mockUser } from "@/lib/mockData";

export default function Dashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [aiAssistantOpen, setAiAssistantOpen] = useState(false);
  const { currentUser, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();

  // Use authenticated user ID or fallback to mock for demo
  const userId = currentUser ? parseInt(currentUser.uid) : mockUser.id;

  return (
    <div className="min-h-screen bg-sky-light dark:bg-navy-deep text-gray-900 dark:text-white transition-colors duration-300">
      <AnimatedBackground />
      
      <div className="flex min-h-screen relative z-10">
        <Sidebar 
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          onToggle={() => setSidebarOpen(!sidebarOpen)}
        />
        
        <main className="flex-1 lg:ml-64">
          <div className="p-6">
            {/* Welcome Section */}
            <div className="mb-8">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Welcome back, <span className="text-blue-600">{mockUser.fullName}</span>! 👋
                  </h1>
                  <p className="text-gray-600 dark:text-gray-300">
                    Here's your health summary for today
                  </p>
                </div>
                <div className="flex items-center space-x-2 mt-4 sm:mt-0">
                  <Badge variant="secondary" className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300">
                    <ShieldCheck className="w-4 h-4 mr-1" />
                    Low Risk
                  </Badge>
                  <Button variant="ghost" size="icon" onClick={toggleTheme}>
                    {theme === 'dark' ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Bell className="w-5 h-5" />
                  </Button>
                  <Button variant="ghost" size="icon" onClick={logout}>
                    <LogOut className="w-5 h-5" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Health Metrics */}
            <HealthMetrics />

            {/* Smartwatch Integration */}
            <div className="mb-8">
              <SmartwatchWidget />
            </div>

            {/* Quick Actions */}
            <QuickActions onAiAssistantClick={() => setAiAssistantOpen(true)} />

            {/* Appointments Overview */}
            <AppointmentsList />

            {/* Tabbed Health Dashboard */}
            <TabbedHealthDashboard />
          </div>
        </main>
      </div>

      {/* AI Assistant Modal */}
      <AIAssistant
        isOpen={aiAssistantOpen}
        onClose={() => setAiAssistantOpen(false)}
        userId={userId}
      />
    </div>
  );
}
