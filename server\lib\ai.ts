import OpenAI from "openai";

// the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY || "default_key"
});

export async function getHealthAssistantResponse(
  message: string, 
  userHealthData?: any
): Promise<string> {
  try {
    const systemPrompt = `You are a helpful AI health assistant for MediKey medical record system. 
    You help patients understand their medical records, track symptoms, and provide general health insights.
    
    IMPORTANT: You are not a replacement for professional medical advice. Always recommend consulting healthcare providers for medical decisions.
    
    Be helpful, empathetic, and informative. Use the user's health data context when available.
    
    ${userHealthData ? `User's recent health context: ${JSON.stringify(userHealthData)}` : ''}`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: message }
      ],
      max_tokens: 300,
      temperature: 0.7
    });

    return response.choices[0].message.content || "I'm sorry, I couldn't process your request right now. Please try again.";
  } catch (error) {
    console.error("AI Assistant error:", error);
    return "I'm currently experiencing technical difficulties. Please try again later or consult your healthcare provider for immediate concerns.";
  }
}

export async function generateRecordSummary(recordContent: string): Promise<string> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are a medical AI that creates concise, patient-friendly summaries of medical records. Focus on key findings, recommendations, and next steps."
        },
        {
          role: "user",
          content: `Please provide a brief summary of this medical record: ${recordContent}`
        }
      ],
      max_tokens: 150,
      temperature: 0.3
    });

    return response.choices[0].message.content || "Summary not available";
  } catch (error) {
    console.error("Record summary error:", error);
    return "Summary generation failed";
  }
}

export async function analyzeHealthTrends(healthData: any[]): Promise<string> {
  try {
    if (!healthData || healthData.length === 0) {
      return "No health data available for analysis.";
    }

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are a health analytics AI. Analyze health metrics trends and provide insights and recommendations. Be specific about patterns you observe."
        },
        {
          role: "user",
          content: `Analyze these health metrics and provide insights: ${JSON.stringify(healthData)}`
        }
      ],
      max_tokens: 200,
      temperature: 0.5
    });

    return response.choices[0].message.content || "Analysis not available";
  } catch (error) {
    console.error("Health trends analysis error:", error);
    return "Health trends analysis failed";
  }
}
