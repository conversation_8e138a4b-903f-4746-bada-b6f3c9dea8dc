import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, CalendarPlus, MessageCircle, QrCode } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface QuickActionsProps {
  onAiAssistantClick: () => void;
}

export default function QuickActions({ onAiAssistantClick }: QuickActionsProps) {
  const { toast } = useToast();

  const handleUploadRecord = () => {
    toast({
      title: "Upload Record",
      description: "File upload feature would open here. Supported formats: PDF, JPG, PNG",
    });
  };

  const handleBookAppointment = () => {
    toast({
      title: "Book Appointment",
      description: "Appointment booking system would open here with doctor search and scheduling.",
    });
  };

  const handleEmergencyQR = () => {
    toast({
      title: "Emergency QR Code",
      description: "Emergency QR Code would be displayed with essential medical information.",
    });
  };

  const actions = [
    {
      icon: Upload,
      label: "Upload Record",
      color: "hover:bg-yellow-500/10",
      iconBg: "bg-yellow-500/20",
      iconColor: "text-yellow-500",
      onClick: handleUploadRecord
    },
    {
      icon: CalendarPlus,
      label: "Book Appointment",
      color: "hover:bg-pink-500/10",
      iconBg: "bg-pink-500/20",
      iconColor: "text-pink-500",
      onClick: handleBookAppointment
    },
    {
      icon: MessageCircle,
      label: "AI Assistant",
      color: "hover:bg-orange-500/10",
      iconBg: "bg-orange-500/20",
      iconColor: "text-orange-500",
      onClick: onAiAssistantClick
    },
    {
      icon: QrCode,
      label: "Emergency QR",
      color: "hover:bg-red-500/10",
      iconBg: "bg-red-500/20",
      iconColor: "text-red-500",
      onClick: handleEmergencyQR
    }
  ];

  return (
    <Card className="mb-8">
      <CardContent className="p-6">
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {actions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Button
                key={index}
                variant="ghost"
                className={`flex flex-col items-center space-y-2 p-4 h-auto ${action.color} transition-colors group`}
                onClick={action.onClick}
              >
                <div className={`w-12 h-12 ${action.iconBg} rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform`}>
                  <Icon className={`w-6 h-6 ${action.iconColor}`} />
                </div>
                <span className="text-sm font-medium">{action.label}</span>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
