import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Stethoscope, Users, FileText, Brain, Calendar, Plus, Search } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

export default function DoctorPanel() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [newNote, setNewNote] = useState('');
  const queryClient = useQueryClient();

  // Mock patients data - in real app this would come from API
  const { data: patients = [] } = useQuery({
    queryKey: ['/api/patients'],
    queryFn: () => Promise.resolve([
      {
        id: 1,
        name: "John Doe",
        age: 45,
        bloodType: "O+",
        lastVisit: "2024-06-10",
        conditions: ["Hypertension", "Type 2 Diabetes"],
        status: "stable"
      },
      {
        id: 2,
        name: "Jane Smith",
        age: 32,
        bloodType: "A-",
        lastVisit: "2024-06-12",
        conditions: ["Asthma"],
        status: "follow-up"
      },
      {
        id: 3,
        name: "Bob Johnson",
        age: 67,
        bloodType: "B+",
        lastVisit: "2024-06-08",
        conditions: ["Arthritis", "High Cholesterol"],
        status: "critical"
      }
    ])
  });

  const addNoteMutation = useMutation({
    mutationFn: async (data: { patientId: number; note: string; type: string }) => {
      return new Promise((resolve) => {
        setTimeout(() => resolve({ id: Date.now(), ...data }), 1000);
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/patients'] });
      setNewNote('');
    }
  });

  const filteredPatients = patients.filter((patient: any) =>
    patient.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'follow-up': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'stable': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Stethoscope className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Doctor Panel</h1>
              <p className="text-gray-600 dark:text-gray-300">Patient management dashboard</p>
            </div>
          </div>
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Dr. Sarah Wilson
          </Badge>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Search className="h-5 w-5 text-gray-400" />
              <Input
                placeholder="Search patients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-md"
              />
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="patients" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="patients" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Patients
            </TabsTrigger>
            <TabsTrigger value="records" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Records
            </TabsTrigger>
            <TabsTrigger value="ai-insights" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              AI Insights
            </TabsTrigger>
            <TabsTrigger value="schedule" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Schedule
            </TabsTrigger>
          </TabsList>

          {/* Patients Tab */}
          <TabsContent value="patients" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredPatients.map((patient: any) => (
                <Card key={patient.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{patient.name}</CardTitle>
                        <CardDescription>
                          Age: {patient.age} • Blood Type: {patient.bloodType}
                        </CardDescription>
                      </div>
                      <Badge className={getStatusColor(patient.status)}>
                        {patient.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Conditions:</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {patient.conditions.map((condition: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {condition}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        Last visit: {patient.lastVisit}
                      </span>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            size="sm" 
                            onClick={() => setSelectedPatient(patient)}
                          >
                            View Details
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>{patient.name} - Patient Details</DialogTitle>
                            <DialogDescription>
                              Comprehensive patient information and notes
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label>Age</Label>
                                <p className="font-medium">{patient.age} years</p>
                              </div>
                              <div>
                                <Label>Blood Type</Label>
                                <p className="font-medium">{patient.bloodType}</p>
                              </div>
                            </div>
                            
                            <div>
                              <Label>Add Clinical Note</Label>
                              <Textarea
                                placeholder="Enter clinical observations, treatment notes, or recommendations..."
                                value={newNote}
                                onChange={(e) => setNewNote(e.target.value)}
                                className="mt-2"
                              />
                              <div className="flex gap-2 mt-2">
                                <Select defaultValue="general">
                                  <SelectTrigger className="w-40">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="general">General Note</SelectItem>
                                    <SelectItem value="diagnosis">Diagnosis</SelectItem>
                                    <SelectItem value="treatment">Treatment</SelectItem>
                                    <SelectItem value="prescription">Prescription</SelectItem>
                                  </SelectContent>
                                </Select>
                                <Button 
                                  onClick={() => addNoteMutation.mutate({
                                    patientId: patient.id,
                                    note: newNote,
                                    type: 'general'
                                  })}
                                  disabled={!newNote.trim() || addNoteMutation.isPending}
                                >
                                  <Plus className="h-4 w-4 mr-2" />
                                  Add Note
                                </Button>
                              </div>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Records Tab */}
          <TabsContent value="records">
            <Card>
              <CardHeader>
                <CardTitle>Medical Records Review</CardTitle>
                <CardDescription>
                  Review and analyze patient medical records
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select a patient to view their medical records</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* AI Insights Tab */}
          <TabsContent value="ai-insights">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-purple-600" />
                    Treatment Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <h4 className="font-medium text-purple-800 dark:text-purple-400">
                        John Doe - Diabetes Management
                      </h4>
                      <p className="text-sm text-purple-700 dark:text-purple-300 mt-1">
                        Consider adjusting Metformin dosage based on recent HbA1c levels
                      </p>
                    </div>
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <h4 className="font-medium text-blue-800 dark:text-blue-400">
                        Jane Smith - Asthma Follow-up
                      </h4>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                        Recommend spirometry test due to increased inhaler usage
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Drug Interaction Alerts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200">
                      <h4 className="font-medium text-yellow-800 dark:text-yellow-400">
                        ⚠️ Moderate Interaction
                      </h4>
                      <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                        Lisinopril + Potassium supplements in Bob Johnson
                      </p>
                    </div>
                    <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <h4 className="font-medium text-green-800 dark:text-green-400">
                        ✓ No Critical Interactions
                      </h4>
                      <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                        All other patient medications are compatible
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Schedule Tab */}
          <TabsContent value="schedule">
            <Card>
              <CardHeader>
                <CardTitle>Today's Schedule</CardTitle>
                <CardDescription>June 13, 2025</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { time: '9:00 AM', patient: 'John Doe', type: 'Follow-up', status: 'confirmed' },
                    { time: '10:30 AM', patient: 'Jane Smith', type: 'Check-up', status: 'confirmed' },
                    { time: '2:00 PM', patient: 'Bob Johnson', type: 'Consultation', status: 'pending' },
                    { time: '3:30 PM', patient: 'Mary Wilson', type: 'Emergency', status: 'urgent' }
                  ].map((appointment, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="text-center">
                          <p className="font-medium">{appointment.time}</p>
                        </div>
                        <div>
                          <p className="font-medium">{appointment.patient}</p>
                          <p className="text-sm text-gray-600">{appointment.type}</p>
                        </div>
                      </div>
                      <Badge className={
                        appointment.status === 'urgent' ? 'bg-red-100 text-red-800' :
                        appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }>
                        {appointment.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}