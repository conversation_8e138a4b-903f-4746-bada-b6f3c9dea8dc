2025-06-13 15:08:17,203 - semgrep.notifications - WARNING - METRICS: Using configs from the Registry (like --config=p/ci) reports pseudonymous rule metrics to semgrep.dev.
To disable Registry rule metrics, use "--metrics=off".
Using configs only from local files (like --config=xyz.yml) does not enable metrics.

More information: https://semgrep.dev/docs/metrics

2025-06-13 15:08:17,204 - semgrep.run_scan - DEBUG - semgrep version 1.4.0
2025-06-13 15:08:17,208 - semgrep.git - DEBUG - Failed to get project url from 'git ls-remote': Command failed with exit code: 128
-----
Command failed with output:
fatal: No remote configured to list refs from.


Failed to run 'git ls-remote --get-url'. Possible reasons:

- the git binary is not available
- the current working directory is not a git repository
- the baseline commit is not a parent of the current commit
    (if you are running through semgrep-app, check if you are setting `SEMGREP_BRANCH` or `SEMGREP_BASELINE_COMMIT` properly)
- the current working directory is not marked as safe
    (fix with `git config --global --add safe.directory $(pwd)`)

Try running the command yourself to debug the issue.
2025-06-13 15:08:17,208 - semgrep.config_resolver - DEBUG - Loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-13 15:08:17,210 - semgrep.config_resolver - DEBUG - Done loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-13 15:08:17,212 - semgrep.config_resolver - DEBUG - Saving rules to /tmp/semgrep-i3006jz_.rules
2025-06-13 15:08:17,856 - semgrep.rule_lang - DEBUG - semgrep-core validation response: valid=True
2025-06-13 15:08:17,856 - semgrep.rule_lang - DEBUG - semgrep-core validation succeeded
2025-06-13 15:08:17,856 - semgrep.rule_lang - DEBUG - RPC validation succeeded
2025-06-13 15:08:17,857 - semgrep.config_resolver - DEBUG - loaded 1 configs in 0.6485068798065186
2025-06-13 15:08:17,934 - semgrep.run_scan - VERBOSE - running 712 rules from 1 config /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json_0
2025-06-13 15:08:17,934 - semgrep.run_scan - VERBOSE - No .semgrepignore found. Using default .semgrepignore rules. See the docs for the list of default ignores: https://semgrep.dev/docs/cli-usage/#ignore-files
2025-06-13 15:08:17,936 - semgrep.run_scan - VERBOSE - Rules:
2025-06-13 15:08:17,936 - semgrep.run_scan - VERBOSE - <SKIPPED DATA (too many entries; use --max-log-list-entries)>
2025-06-13 15:08:18,336 - semgrep.core_runner - DEBUG - Passing whole rules directly to semgrep_core
2025-06-13 15:08:18,495 - semgrep.core_runner - DEBUG - Running Semgrep engine with command:
2025-06-13 15:08:18,495 - semgrep.core_runner - DEBUG - /tmp/_MEIa03rJK/semgrep/bin/opengrep-core -json -rules /tmp/tmp5pe1ru6i.json -j 8 -targets /tmp/tmpcyhd7z92 -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
