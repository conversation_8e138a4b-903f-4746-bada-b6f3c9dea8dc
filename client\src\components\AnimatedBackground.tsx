import { useEffect, useRef } from "react";

export default function AnimatedBackground() {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create floating molecules
    const createMolecule = (x: number, y: number, color: string, size: number) => {
      const molecule = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      molecule.setAttribute("cx", x.toString());
      molecule.setAttribute("cy", y.toString());
      molecule.setAttribute("r", size.toString());
      molecule.setAttribute("fill", color);
      molecule.setAttribute("opacity", "0.6");
      molecule.classList.add("molecule-float", "molecule-glow");
      
      return molecule;
    };

    const createConnection = (x1: number, y1: number, x2: number, y2: number) => {
      const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
      line.setAttribute("x1", x1.toString());
      line.setAttribute("y1", y1.toString());
      line.setAttribute("x2", x2.toString());
      line.setAttribute("y2", y2.toString());
      line.setAttribute("stroke", "rgb(100, 116, 139)");
      line.setAttribute("stroke-width", "1");
      line.setAttribute("opacity", "0.3");
      
      return line;
    };

    const svg = container.querySelector("svg");
    if (svg) {
      // Clear existing content
      svg.innerHTML = `
        <defs>
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
      `;

      // Add molecules and connections
      const molecules = [
        { x: 200, y: 200, color: "#3CB371", size: 4 },
        { x: 400, y: 300, color: "#FFD700", size: 3 },
        { x: 600, y: 150, color: "#FF69B4", size: 5 },
        { x: 800, y: 400, color: "#FFA500", size: 3 },
        { x: 300, y: 500, color: "#FF6347", size: 4 },
        { x: 700, y: 250, color: "#3CB371", size: 3 }
      ];

      // Add connections
      const connections = [
        [0, 1], [1, 2], [2, 3], [3, 4], [4, 5], [5, 0]
      ];

      connections.forEach(([start, end]) => {
        const line = createConnection(
          molecules[start].x,
          molecules[start].y,
          molecules[end].x,
          molecules[end].y
        );
        svg.appendChild(line);
      });

      // Add molecules
      molecules.forEach(mol => {
        const molecule = createMolecule(mol.x, mol.y, mol.color, mol.size);
        molecule.setAttribute("filter", "url(#glow)");
        svg.appendChild(molecule);
      });
    }
  }, []);

  return (
    <div 
      ref={containerRef}
      className="fixed inset-0 overflow-hidden pointer-events-none z-0"
    >
      <svg 
        className="absolute inset-0 w-full h-full opacity-10 dark:opacity-5" 
        viewBox="0 0 1000 600"
        preserveAspectRatio="xMidYMid slice"
      >
        {/* SVG content will be populated by useEffect */}
      </svg>
    </div>
  );
}
