🔐 Firebase Integration
✔ Set up Firebase Auth for Sign In/Sign Up

Email/password auth only

Use Firebase Auth to protect pages and routes

Assign roles (doctor, patient) via Firestore or metadata

env
Copy
Edit
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_APP_ID=your_app_id
🤖 AI Assistant (OpenAI-based)
✔ Use OpenAI (GPT-3.5 or GPT-4) via /assistant route

Use OPENAI_API_KEY from .env

Simulate user → assistant chat

Show AI messages with orange background and avatars

Assistant should:

Summarize health records

Respond to medication questions

Give general wellness advice

Maintain message history in local state or Firestore

env
Copy
Edit
OPENAI_API_KEY=your_openai_key
Sample Request:

ts
Copy
Edit
const response = await fetch("https://api.openai.com/v1/chat/completions", {
  method: "POST",
  headers: {
    Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    model: "gpt-3.5-turbo",
    messages: [
      { role: "system", content: "You are a helpful AI health assistant." },
      { role: "user", content: userInput },
    ],
  }),
});
🎯 MUST-FINISH FEATURES
✅ Fix Theme Toggle (Light & Dark)

Light: #E6F0FF

Dark: #0B1E3F

Global toggle across all pages

✅ Smartwatch Integration Widget

Simulate real-time metrics

Display in grid: HR, SpO2, Temp, Sleep, Steps

✅ Upload Medical Records

Accept PDFs/images

Simulate OCR & category (Prescription, Report, etc.)

Save record metadata to local state or Firestore

✅ Emergency Access Page

Generate QR code (use qrcode npm lib)

Show mock read-only record

Simulate timeout or OTP unlock

✅ Doctor Panel

Role-based UI

View patients, add notes

View AI summaries

✅ Family Vault

Manage dependents (child, parent)

View separate record sets

Green-themed UI section

✅ Appointment System

Schedule view + reminders

Pink theme card

Date/time picker & save button

✅ AI Health Summary

On upload → click “Summarize”

Use OpenAI to generate a short case summary

Display in record card or dashboard

✅ Dashboard Tabs

Tabs: Health Dashboard | Records | Family

Charts using Recharts (BP, Glucose, Sleep, etc.)

✅ Search & Filters

Keyword search across records

Filter by tag (prescription, allergy, etc.)

🧾 Bonus
Mobile responsiveness

Animated background (SVG molecule-style)

Lucide icons

Smooth transitions (Framer Motion or Tailwind animations)

Loading + error handling on all API calls

✨ Reminder
This project should reflect the exact layout, theme colors, and structure shown in the approved project screenshots (PDF) and final UI version.