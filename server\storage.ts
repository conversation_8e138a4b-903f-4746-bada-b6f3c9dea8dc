import { 
  users, 
  medicalRecords, 
  appointments, 
  familyMembers, 
  healthMetrics,
  aiChatMessages,
  type User, 
  type InsertUser,
  type MedicalRecord,
  type InsertMedicalRecord,
  type Appointment,
  type InsertAppointment,
  type FamilyMember,
  type InsertFamilyMember,
  type HealthMetrics,
  type InsertHealthMetrics,
  type AiChatMessage,
  type InsertAiChatMessage
} from "@shared/schema";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<InsertUser>): Promise<User | undefined>;

  // Medical record methods
  getMedicalRecords(userId: number): Promise<MedicalRecord[]>;
  getMedicalRecord(id: number): Promise<MedicalRecord | undefined>;
  createMedicalRecord(record: InsertMedicalRecord): Promise<MedicalRecord>;
  updateMedicalRecord(id: number, updates: Partial<InsertMedicalRecord>): Promise<MedicalRecord | undefined>;
  deleteMedicalRecord(id: number): Promise<boolean>;

  // Appointment methods
  getAppointments(userId: number): Promise<Appointment[]>;
  getUpcomingAppointments(userId: number): Promise<Appointment[]>;
  createAppointment(appointment: InsertAppointment): Promise<Appointment>;
  updateAppointment(id: number, updates: Partial<InsertAppointment>): Promise<Appointment | undefined>;
  deleteAppointment(id: number): Promise<boolean>;

  // Family member methods
  getFamilyMembers(userId: number): Promise<FamilyMember[]>;
  createFamilyMember(member: InsertFamilyMember): Promise<FamilyMember>;
  updateFamilyMember(id: number, updates: Partial<InsertFamilyMember>): Promise<FamilyMember | undefined>;
  deleteFamilyMember(id: number): Promise<boolean>;

  // Health metrics methods
  getLatestHealthMetrics(userId: number): Promise<HealthMetrics | undefined>;
  getHealthMetricsHistory(userId: number, days?: number): Promise<HealthMetrics[]>;
  createHealthMetrics(metrics: InsertHealthMetrics): Promise<HealthMetrics>;

  // AI chat methods
  getAiChatHistory(userId: number): Promise<AiChatMessage[]>;
  createAiChatMessage(message: InsertAiChatMessage): Promise<AiChatMessage>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private medicalRecords: Map<number, MedicalRecord>;
  private appointments: Map<number, Appointment>;
  private familyMembers: Map<number, FamilyMember>;
  private healthMetrics: Map<number, HealthMetrics>;
  private aiChatMessages: Map<number, AiChatMessage>;
  private currentId: number;

  constructor() {
    this.users = new Map();
    this.medicalRecords = new Map();
    this.appointments = new Map();
    this.familyMembers = new Map();
    this.healthMetrics = new Map();
    this.aiChatMessages = new Map();
    this.currentId = 1;
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const patientId = `MK${String(id).padStart(8, '0')}`;
    const user: User = { 
      ...insertUser,
      id, 
      patientId,
      createdAt: new Date(),
      address: insertUser.address || null,
      dateOfBirth: insertUser.dateOfBirth || null,
      bloodGroup: insertUser.bloodGroup || null,
      phoneNumber: insertUser.phoneNumber || null,
      emergencyContact: insertUser.emergencyContact || null,
      role: insertUser.role || "patient"
    };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, updates: Partial<InsertUser>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;
    
    const updatedUser = { ...user, ...updates };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  // Medical record methods
  async getMedicalRecords(userId: number): Promise<MedicalRecord[]> {
    return Array.from(this.medicalRecords.values())
      .filter(record => record.userId === userId)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  async getMedicalRecord(id: number): Promise<MedicalRecord | undefined> {
    return this.medicalRecords.get(id);
  }

  async createMedicalRecord(insertRecord: InsertMedicalRecord): Promise<MedicalRecord> {
    const id = this.currentId++;
    const record: MedicalRecord = {
      ...insertRecord,
      id,
      createdAt: new Date(),
      aiSummary: null,
      status: insertRecord.status || "normal",
      hospitalName: insertRecord.hospitalName || null,
      doctorName: insertRecord.doctorName || null,
      description: insertRecord.description || null,
      tags: insertRecord.tags || null,
      fileUrl: insertRecord.fileUrl || null
    };
    this.medicalRecords.set(id, record);
    return record;
  }

  async updateMedicalRecord(id: number, updates: Partial<InsertMedicalRecord>): Promise<MedicalRecord | undefined> {
    const record = this.medicalRecords.get(id);
    if (!record) return undefined;
    
    const updatedRecord = { ...record, ...updates };
    this.medicalRecords.set(id, updatedRecord);
    return updatedRecord;
  }

  async deleteMedicalRecord(id: number): Promise<boolean> {
    return this.medicalRecords.delete(id);
  }

  // Appointment methods
  async getAppointments(userId: number): Promise<Appointment[]> {
    return Array.from(this.appointments.values())
      .filter(appointment => appointment.userId === userId)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  async getUpcomingAppointments(userId: number): Promise<Appointment[]> {
    const now = new Date();
    return Array.from(this.appointments.values())
      .filter(appointment => 
        appointment.userId === userId && 
        new Date(appointment.date) >= now &&
        appointment.status === 'scheduled'
      )
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  async createAppointment(insertAppointment: InsertAppointment): Promise<Appointment> {
    const id = this.currentId++;
    const appointment: Appointment = {
      ...insertAppointment,
      id,
      createdAt: new Date(),
      status: insertAppointment.status || "scheduled",
      notes: insertAppointment.notes || null
    };
    this.appointments.set(id, appointment);
    return appointment;
  }

  async updateAppointment(id: number, updates: Partial<InsertAppointment>): Promise<Appointment | undefined> {
    const appointment = this.appointments.get(id);
    if (!appointment) return undefined;
    
    const updatedAppointment = { ...appointment, ...updates };
    this.appointments.set(id, updatedAppointment);
    return updatedAppointment;
  }

  async deleteAppointment(id: number): Promise<boolean> {
    return this.appointments.delete(id);
  }

  // Family member methods
  async getFamilyMembers(userId: number): Promise<FamilyMember[]> {
    return Array.from(this.familyMembers.values())
      .filter(member => member.userId === userId);
  }

  async createFamilyMember(insertMember: InsertFamilyMember): Promise<FamilyMember> {
    const id = this.currentId++;
    const member: FamilyMember = {
      ...insertMember,
      id,
      createdAt: new Date(),
      bloodGroup: insertMember.bloodGroup || null,
      phoneNumber: insertMember.phoneNumber || null,
      age: insertMember.age || null,
      lastCheckup: insertMember.lastCheckup || null
    };
    this.familyMembers.set(id, member);
    return member;
  }

  async updateFamilyMember(id: number, updates: Partial<InsertFamilyMember>): Promise<FamilyMember | undefined> {
    const member = this.familyMembers.get(id);
    if (!member) return undefined;
    
    const updatedMember = { ...member, ...updates };
    this.familyMembers.set(id, updatedMember);
    return updatedMember;
  }

  async deleteFamilyMember(id: number): Promise<boolean> {
    return this.familyMembers.delete(id);
  }

  // Health metrics methods
  async getLatestHealthMetrics(userId: number): Promise<HealthMetrics | undefined> {
    const userMetrics = Array.from(this.healthMetrics.values())
      .filter(metrics => metrics.userId === userId)
      .sort((a, b) => new Date(b.recordedAt || new Date()).getTime() - new Date(a.recordedAt || new Date()).getTime());
    
    return userMetrics[0];
  }

  async getHealthMetricsHistory(userId: number, days = 30): Promise<HealthMetrics[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return Array.from(this.healthMetrics.values())
      .filter(metrics => 
        metrics.userId === userId && 
        new Date(metrics.recordedAt || new Date()) >= cutoffDate
      )
      .sort((a, b) => new Date(a.recordedAt || new Date()).getTime() - new Date(b.recordedAt || new Date()).getTime());
  }

  async createHealthMetrics(insertMetrics: InsertHealthMetrics): Promise<HealthMetrics> {
    const id = this.currentId++;
    const metrics: HealthMetrics = {
      ...insertMetrics,
      id,
      recordedAt: insertMetrics.recordedAt || new Date(),
      heartRate: insertMetrics.heartRate || null,
      bloodPressureSystolic: insertMetrics.bloodPressureSystolic || null,
      bloodPressureDiastolic: insertMetrics.bloodPressureDiastolic || null,
      oxygenSaturation: insertMetrics.oxygenSaturation || null,
      bloodSugar: insertMetrics.bloodSugar || null,
      steps: insertMetrics.steps || null,
      sleepHours: insertMetrics.sleepHours || null,
      weight: insertMetrics.weight || null,
      bmi: insertMetrics.bmi || null,
      temperature: insertMetrics.temperature || null
    };
    this.healthMetrics.set(id, metrics);
    return metrics;
  }

  // AI chat methods
  async getAiChatHistory(userId: number): Promise<AiChatMessage[]> {
    return Array.from(this.aiChatMessages.values())
      .filter(message => message.userId === userId)
      .sort((a, b) => new Date(a.createdAt || new Date()).getTime() - new Date(b.createdAt || new Date()).getTime());
  }

  async createAiChatMessage(insertMessage: InsertAiChatMessage): Promise<AiChatMessage> {
    const id = this.currentId++;
    const message: AiChatMessage = {
      ...insertMessage,
      id,
      createdAt: new Date(),
      category: insertMessage.category || null
    };
    this.aiChatMessages.set(id, message);
    return message;
  }
}

export const storage = new MemStorage();
