import { createContext, useContext, useEffect, useState } from 'react';

// Mock user type for demo
interface MockUser {
  uid: string;
  email: string;
  displayName?: string;
}

interface AuthContextType {
  currentUser: MockUser | null;
  login: (email: string, password: string) => Promise<any>;
  signup: (email: string, password: string) => Promise<any>;
  logout: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType);

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentUser, setCurrentUser] = useState<MockUser | null>(null);
  const [loading, setLoading] = useState(true);

  function signup(email: string, password: string) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newUser: MockUser = {
          uid: Date.now().toString(),
          email,
          displayName: email.split('@')[0]
        };
        setCurrentUser(newUser);
        localStorage.setItem('medikey-user', JSON.stringify(newUser));
        resolve(newUser);
      }, 1000);
    });
  }

  function login(email: string, password: string) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (email === '<EMAIL>' && password === 'demo123') {
          const user: MockUser = {
            uid: 'demo-user',
            email,
            displayName: 'Demo User'
          };
          setCurrentUser(user);
          localStorage.setItem('medikey-user', JSON.stringify(user));
          resolve(user);
        } else {
          reject(new Error('Invalid credentials. Use <EMAIL> / demo123'));
        }
      }, 1000);
    });
  }

  function logout() {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        setCurrentUser(null);
        localStorage.removeItem('medikey-user');
        resolve();
      }, 500);
    });
  }

  useEffect(() => {
    const savedUser = localStorage.getItem('medikey-user');
    if (savedUser) {
      setCurrentUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const value = {
    currentUser,
    login,
    signup,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}