import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { 
  insertUserSchema, 
  insertMedicalRecordSchema,
  insertAppointmentSchema,
  insertFamilyMemberSchema,
  insertHealthMetricsSchema,
  insertAiChatMessageSchema
} from "@shared/schema";
import { getHealthAssistantResponse, generateRecordSummary, analyzeHealthTrends } from "./lib/ai";

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth routes
  app.post("/api/auth/signup", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      const existingUser = await storage.getUserByEmail(userData.email);
      
      if (existingUser) {
        return res.status(400).json({ message: "User already exists" });
      }

      const user = await storage.createUser(userData);
      res.json({ user: { ...user, password: undefined } });
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/auth/signin", async (req, res) => {
    try {
      const { email, password } = req.body;
      const user = await storage.getUserByEmail(email);
      
      if (!user || user.password !== password) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      res.json({ user: { ...user, password: undefined } });
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  // User routes
  app.get("/api/users/:id", async (req, res) => {
    try {
      const user = await storage.getUser(parseInt(req.params.id));
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json({ ...user, password: undefined });
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  // Medical records routes
  app.get("/api/medical-records/:userId", async (req, res) => {
    try {
      const records = await storage.getMedicalRecords(parseInt(req.params.userId));
      res.json(records);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/medical-records", async (req, res) => {
    try {
      const recordData = insertMedicalRecordSchema.parse(req.body);
      const record = await storage.createMedicalRecord(recordData);
      
      // Generate AI summary if description exists
      if (record.description) {
        const summary = await generateRecordSummary(record.description);
        const updatedRecord = await storage.updateMedicalRecord(record.id, { description: record.description });
        if (updatedRecord) {
          updatedRecord.aiSummary = summary;
        }
      }
      
      res.json(record);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.put("/api/medical-records/:id", async (req, res) => {
    try {
      const updates = req.body;
      const record = await storage.updateMedicalRecord(parseInt(req.params.id), updates);
      if (!record) {
        return res.status(404).json({ message: "Record not found" });
      }
      res.json(record);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.delete("/api/medical-records/:id", async (req, res) => {
    try {
      const success = await storage.deleteMedicalRecord(parseInt(req.params.id));
      if (!success) {
        return res.status(404).json({ message: "Record not found" });
      }
      res.json({ message: "Record deleted successfully" });
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  // Appointments routes
  app.get("/api/appointments/:userId", async (req, res) => {
    try {
      const appointments = await storage.getAppointments(parseInt(req.params.userId));
      res.json(appointments);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.get("/api/appointments/:userId/upcoming", async (req, res) => {
    try {
      const appointments = await storage.getUpcomingAppointments(parseInt(req.params.userId));
      res.json(appointments);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/appointments", async (req, res) => {
    try {
      const appointmentData = insertAppointmentSchema.parse(req.body);
      const appointment = await storage.createAppointment(appointmentData);
      res.json(appointment);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  // Family members routes
  app.get("/api/family-members/:userId", async (req, res) => {
    try {
      const members = await storage.getFamilyMembers(parseInt(req.params.userId));
      res.json(members);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/family-members", async (req, res) => {
    try {
      const memberData = insertFamilyMemberSchema.parse(req.body);
      const member = await storage.createFamilyMember(memberData);
      res.json(member);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  // Health metrics routes
  app.get("/api/health-metrics/:userId/latest", async (req, res) => {
    try {
      const metrics = await storage.getLatestHealthMetrics(parseInt(req.params.userId));
      res.json(metrics || {});
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.get("/api/health-metrics/:userId/history", async (req, res) => {
    try {
      const days = req.query.days ? parseInt(req.query.days as string) : 30;
      const metrics = await storage.getHealthMetricsHistory(parseInt(req.params.userId), days);
      res.json(metrics);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/health-metrics", async (req, res) => {
    try {
      const metricsData = insertHealthMetricsSchema.parse(req.body);
      const metrics = await storage.createHealthMetrics(metricsData);
      res.json(metrics);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  // AI Assistant routes
  app.post("/api/ai/chat", async (req, res) => {
    try {
      const { message, userId } = req.body;
      
      if (!message || !userId) {
        return res.status(400).json({ message: "Message and userId are required" });
      }

      // Get user's health context
      const latestMetrics = await storage.getLatestHealthMetrics(userId);
      const recentRecords = await storage.getMedicalRecords(userId);
      
      const healthContext = {
        latestMetrics,
        recentRecords: recentRecords.slice(0, 3) // Last 3 records for context
      };

      const response = await getHealthAssistantResponse(message, healthContext);
      
      // Save chat message
      const chatMessage = await storage.createAiChatMessage({
        userId,
        message,
        response,
        category: "general"
      });

      res.json({ response, messageId: chatMessage.id });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/ai/chat-history/:userId", async (req, res) => {
    try {
      const history = await storage.getAiChatHistory(parseInt(req.params.userId));
      res.json(history);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/ai/analyze-trends", async (req, res) => {
    try {
      const { userId } = req.body;
      const healthHistory = await storage.getHealthMetricsHistory(userId, 30);
      const analysis = await analyzeHealthTrends(healthHistory);
      res.json({ analysis });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
