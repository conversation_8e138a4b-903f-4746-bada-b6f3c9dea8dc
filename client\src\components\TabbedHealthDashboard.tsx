import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Activity, 
  Droplets, 
  Weight, 
  HeartPulse, 
  FileText, 
  Radiation, 
  Pill, 
  Eye, 
  Download,
  UserPlus
} from "lucide-react";
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { mockRecentRecords, mockFamilyMembers, mockChartData } from "@/lib/mockData";

export default function TabbedHealthDashboard() {
  const [activeTab, setActiveTab] = useState("health");

  const renderHealthDashboard = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Blood Pressure Chart */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium mb-4 flex items-center space-x-2">
            <Activity className="w-4 h-4 text-red-500" />
            <span>Blood Pressure Trend</span>
          </h3>
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={mockChartData.bloodPressure}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="systolic" stroke="#ef4444" strokeWidth={2} />
                <Line type="monotone" dataKey="diastolic" stroke="#f97316" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Blood Sugar Chart */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium mb-4 flex items-center space-x-2">
            <Droplets className="w-4 h-4 text-blue-500" />
            <span>Blood Sugar Levels</span>
          </h3>
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={mockChartData.bloodSugar}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="glucose" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.2} />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* BMI Tracker */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium mb-4 flex items-center space-x-2">
            <Weight className="w-4 h-4 text-green-500" />
            <span>Body Mass Index</span>
          </h3>
          <div className="h-48 flex items-center justify-center">
            <div className="text-center">
              <div className="w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-white font-bold text-lg">22.5</span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Normal Range</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Heart Rate Variability */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium mb-4 flex items-center space-x-2">
            <HeartPulse className="w-4 h-4 text-purple-500" />
            <span>Heart Rate Variability</span>
          </h3>
          <div className="h-48 flex items-center justify-center">
            <div className="text-center">
              <HeartPulse className="w-8 h-8 text-purple-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">HRV: 45ms - Good</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderRecentRecords = () => (
    <div className="space-y-4">
      {mockRecentRecords.map((record) => {
        const getRecordIcon = (category: string) => {
          switch (category) {
            case "lab_report": return FileText;
            case "imaging": return Radiation;
            case "prescription": return Pill;
            default: return FileText;
          }
        };

        const getStatusColor = (status: string) => {
          switch (status) {
            case "normal": return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300";
            case "active": return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300";
            case "critical": return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300";
            default: return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300";
          }
        };

        const Icon = getRecordIcon(record.category);

        return (
          <div
            key={record.id}
            className="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-yellow-500/50 transition-colors"
          >
            <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
              <Icon className="w-6 h-6 text-yellow-500" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{record.title}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">{record.hospitalName}</p>
              <p className="text-xs text-gray-500 dark:text-gray-500">{record.date}</p>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(record.status)}`}>
                {record.status === "normal" ? "Normal" : record.status === "active" ? "Active" : "Clear"}
              </span>
              <Button variant="ghost" size="icon">
                <Eye className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon">
                <Download className="w-4 h-4" />
              </Button>
            </div>
          </div>
        );
      })}
    </div>
  );

  const renderFamilyVault = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {mockFamilyMembers.map((member) => (
        <Card key={member.id} className="border-green-500/30 hover:border-green-500 transition-colors bg-green-50/50 dark:bg-green-900/10">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                <span className="text-green-500 font-bold">{member.initials}</span>
              </div>
              <div>
                <h3 className="font-medium">{member.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{member.relation}</p>
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Age:</span>
                <span>{member.age}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Blood Group:</span>
                <span>{member.bloodGroup}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Last Checkup:</span>
                <span>{member.lastCheckup}</span>
              </div>
            </div>
            <Button className="w-full mt-4 bg-green-500 hover:bg-green-600 text-white">
              View Records
            </Button>
          </CardContent>
        </Card>
      ))}
      
      {/* Add Family Member Card */}
      <Card className="border-2 border-dashed border-green-500/30 hover:border-green-500 transition-colors bg-green-50/30 dark:bg-green-900/5">
        <CardContent className="p-6 flex flex-col items-center justify-center min-h-[200px]">
          <UserPlus className="w-8 h-8 text-green-500 mb-3" />
          <h3 className="font-medium text-green-500 mb-2">Add Family Member</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">
            Add a family member to manage their health records
          </p>
          <Button variant="outline" className="border-green-500 text-green-500 hover:bg-green-500 hover:text-white">
            Add Member
          </Button>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <Card>
      <CardContent className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 mb-6">
            <TabsTrigger value="health">Health Dashboard</TabsTrigger>
            <TabsTrigger value="records">Recent Records</TabsTrigger>
            <TabsTrigger value="family">Family Vault</TabsTrigger>
          </TabsList>
          
          <TabsContent value="health" className="tab-content">
            {renderHealthDashboard()}
          </TabsContent>
          
          <TabsContent value="records" className="tab-content">
            {renderRecentRecords()}
          </TabsContent>
          
          <TabsContent value="family" className="tab-content">
            {renderFamilyVault()}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
